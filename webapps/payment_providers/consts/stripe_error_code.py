from django.utils.translation import gettext_lazy as _
from lib.enums import StrChoicesEnum


class DeclineCode(StrChoicesEnum):
    """
    Stripe documentation: https://stripe.com/docs/declines/codes
    """

    AUTHENTICATION_REQUIRED = (
        'authentication_required',
        'The card was declined as the transaction requires authentication.',
    )
    APPROVE_WITH_ID = ('approve_with_id', 'The payment cannot be authorized.')
    CALL_ISSUER = ('call_issuer', 'The card has been declined for an unknown reason.')
    CARD_NOT_SUPPORTED = (
        'card_not_supported',
        'The card does not support this type of purchase.',
    )
    CARD_VELOCITY_EXCEEDED = (
        'card_velocity_exceeded',
        'The customer has exceeded the balance or credit limit available on their card.',
    )
    CURRENCY_NOT_SUPPORTED = (
        'currency_not_supported',
        'The card does not support the specified currency.',
    )
    DO_NOT_HONOR = ('do_not_honor', 'The card has been declined for an unknown reason.')
    DO_NOT_TRY_AGAIN = ('do_not_try_again', 'The card has been declined for an unknown reason.')
    DUPLICATE_TRANSACTION = (
        'duplicate_transaction',
        (
            'A transaction with identical amount and credit card information was submitted very '
            'recently.'
        ),
    )
    EXPIRED_CARD = (
        'expired_card',
        'The card has expired.	The customer should use another card.',
    )
    FRAUDULENT = (
        'fraudulent',
        'The payment has been declined as Stripe suspects it is fraudulent.',
    )
    GENERIC_DECLINE = ('generic_decline', 'The card has been declined for an unknown reason.')
    INCORRECT_NUMBER = ('incorrect_number', 'The card number is incorrect.')
    INCORRECT_CVC = ('incorrect_cvc', 'The CVC number is incorrect.')
    INCORRECT_PIN = (
        'incorrect_pin',
        (
            'The PIN entered is incorrect. This decline code only applies to payments made with a '
            'card reader.'
        ),
    )
    INCORRECT_ZIP = ('incorrect_zip', 'The ZIP/postal code is incorrect.')
    INSUFFICIENT_FUNDS = (
        'insufficient_funds',
        'The card has insufficient funds to complete the purchase.',
    )
    INVALID_ACCOUNT = (
        'invalid_account',
        'The card, or account the card is connected to, is invalid.',
    )
    INVALID_AMOUNT = (
        'invalid_amount',
        'The payment amount is invalid, or exceeds the amount that is allowed.',
    )
    INVALID_CVC = ('invalid_cvc', 'The CVC number is incorrect.')
    INVALID_EXPIRY_MONTH = ('invalid_expiry_month', 'The expiration month is invalid.')
    INVALID_EXPIRY_YEAR = ('invalid_expiry_year', 'The expiration year is invalid.')
    INVALID_NUMBER = ('invalid_number', 'The card number is incorrect.')
    INVALID_PIN = (
        'invalid_pin',
        (
            'The PIN entered is incorrect. This decline code only applies to payments made with a '
            'card reader.'
        ),
    )
    ISSUER_NOT_AVAILABLE = (
        'issuer_not_available',
        'The card issuer could not be reached, so the payment could not be authorized.',
    )
    LOST_CARD = ('lost_card', 'The payment has been declined because the card is reported lost.')
    MERCHANT_BLACKLIST = (
        'merchant_blacklist',
        (
            'The payment has been declined because it matches a value on the Stripe user\'s block '
            'list.'
        ),
    )
    NEW_ACCOUNT_INFORMATION_AVAILABLE = (
        'new_account_information_available',
        'The card, or account the card is connected to, is invalid.',
    )
    NO_ACTION_TAKEN = ('no_action_taken', 'The card has been declined for an unknown reason.')
    NOT_PERMITTED = ('not_permitted', 'The payment is not permitted.')
    OFFLINE_PIN_REQUIRED = (
        'offline_pin_required',
        'The card has been declined as it requires a PIN.',
    )
    ONLINE_OR_OFFLINE_PIN_REQUIRED = (
        'online_or_offline_pin_required',
        'The card has been declined as it requires a PIN.',
    )
    PICKUP_CARD = (
        'pickup_card',
        (
            'The card cannot be used to make this payment (it is possible it has been reported '
            'lost or stolen).'
        ),
    )
    PIN_TRY_EXCEEDED = (
        'pin_try_exceeded',
        'The allowable number of PIN tries has been exceeded.',
    )
    PROCESSING_ERROR = ('processing_error', 'An error occurred while processing the card.')
    REENTER_TRANSACTION = (
        'reenter_transaction',
        'The payment could not be processed by the issuer for an unknown reason.',
    )
    RESTRICTED_CARD = (
        'restricted_card',
        (
            'The card cannot be used to make this payment (it is possible it has been reported '
            'lost or stolen).'
        ),
    )
    REVOCATION_OF_ALL_AUTHORIZATIONS = (
        'revocation_of_all_authorizations',
        'The card has been declined for an unknown reason.',
    )
    REVOCATION_OF_AUTHORIZATION = (
        'revocation_of_authorization',
        'The card has been declined for an unknown reason.',
    )
    SECURITY_VIOLATION = (
        'security_violation',
        'The card has been declined for an unknown reason.',
    )
    SERVICE_NOT_ALLOWED = (
        'service_not_allowed',
        'The card has been declined for an unknown reason.',
    )
    STOLEN_CARD = (
        'stolen_card',
        'The payment has been declined because the card is reported stolen.',
    )
    STOP_PAYMENT_ORDER = (
        'stop_payment_order',
        'The card has been declined for an unknown reason.',
    )
    TESTMODE_DECLINE = ('testmode_decline', 'A Stripe test card number was used.')
    TRANSACTION_NOT_ALLOWED = (
        'transaction_not_allowed',
        'The card has been declined for an unknown reason.',
    )
    TRY_AGAIN_LATER = ('try_again_later', 'The card has been declined for an unknown reason.')
    WITHDRAWAL_COUNT_LIMIT_EXCEEDED = (
        'withdrawal_count_limit_exceeded',
        'The customer has exceeded the balance or credit limit available on their card.',
    )


# pylint: disable=duplicate-code
class TapToPayErrorCode(StrChoicesEnum):
    CARD_READ_TIMED_OUT = "card_read_timed_out", _(
        "Payment could not be completed because the transaction timed out, please try again."
    )
    TAP_TO_PAY_DEVICE_TAMPERED = "tap_to_pay_device_tampered", _(
        "We detected a possible security issue. "
        "This could happen if parts of your device "
        "have been replaced or if the device’s software was modified."
    )
    TAP_TO_PAY_NFC_DISABLED = "tap_to_pay_nfc_disabled", _(
        "NFC must be enabled to process Tap to Pay transactions. "
        "Please check your settings and try again."
    )
    LOCATION_SERVICES_DISABLED = "location_services_disabled", _(
        "Location services must be enabled to process Tap to Pay transactions. "
        "Please check your settings and try again."
    )
    TAP_TO_PAY_INSECURE_ENVIRONMENT = "tap_to_pay_insecure_environment", _(
        "We detected a possible security issue. Please ensure screen recording is off, "
        "camera is not active, and developer options are disabled in your settings."
    )


class ApiErrorCode(StrChoicesEnum):
    """
    Stripe documentation: https://stripe.com/docs/error-codes
    """

    ACCOUNT_COUNTRY_INVALID_ADDRESS = (
        'account_country_invalid_address',
        (
            'The country of the business address provided does not match the country of the '
            'account. Businesses must be located in the same country as the account.'
        ),
    )
    ACCOUNT_ERROR_COUNTRY_CHANGE_REQUIRES_ADDITIONAL_STEPS = (
        'account_error_country_change_requires_additional_steps',
        (
            'Your account has already onboarded as a Connect platform. Changing your country '
            'requires additional steps. Please reach out to Stripe support for more information.'
        ),
    )
    ACCOUNT_INVALID = (
        'account_invalid',
        (
            'The account ID provided as a value for the Stripe-Account header is invalid. '
            'Check that your requests are specifying a valid account ID.'
        ),
    )
    ACCOUNT_NUMBER_INVALID = (
        'account_number_invalid',
        (
            'The bank account number provided is invalid (e.g., missing digits). '
            'Bank account information varies from country to country. We recommend creating '
            'validations in your entry forms based on the bank account formats we provide.'
        ),
    )
    ACSS_DEBIT_SESSION_INCOMPLETE = (
        'acss_debit_session_incomplete',
        (
            'The ACSS debit session is not ready to transition to complete status yet. '
            'Please try again the request later.'
        ),
    )
    ALIPAY_UPGRADE_REQUIRED = (
        'alipay_upgrade_required',
        (
            'This method for creating Alipay payments is not supported anymore. '
            'Please upgrade your integration to use Sources instead.'
        ),
    )
    AMOUNT_TOO_LARGE = (
        'amount_too_large',
        (
            'The specified amount is greater than the maximum amount allowed. '
            'Use a lower amount and try again.'
        ),
    )
    AMOUNT_TOO_SMALL = (
        'amount_too_small',
        (
            'The specified amount is less than the minimum amount allowed. '
            'Use a higher amount and try again.'
        ),
    )
    API_KEY_EXPIRED = (
        'api_key_expired',
        (
            'The API key provided has expired. Obtain your current API keys from the '
            'Dashboard and update your integration to use them.'
        ),
    )
    AUTHENTICATION_REQUIRED = (
        'authentication_required',
        (
            'The payment requires authentication to proceed. If your customer is off '
            'session, notify your customer to return to your application and complete the '
            'payment. If you provided the error_on_requires_action parameter, then your customer '
            'should try another card that does not require authentication.'
        ),
    )
    BALANCE_INSUFFICIENT = (
        'balance_insufficient',
        (
            'The transfer or payout could not be completed because the associated account '
            'does not have a sufficient balance available. Create a new transfer or payout '
            'using an amount less than or equal to the account\'s available balance.'
        ),
    )
    BANK_ACCOUNT_DECLINED = (
        'bank_account_declined',
        (
            'The bank account provided can not be used to charge, either '
            'because it is not verified yet or it is not supported.'
        ),
    )
    BANK_ACCOUNT_EXISTS = (
        'bank_account_exists',
        (
            'The bank account provided already exists on the specified Customer object. '
            'If the bank account should also be attached to a different customer, include the '
            'correct customer ID when making the request again.'
        ),
    )
    BANK_ACCOUNT_UNUSABLE = (
        'bank_account_unusable',
        'The bank account provided cannot be used. A different bank account must be used.',
    )
    BANK_ACCOUNT_UNVERIFIED = (
        'bank_account_unverified',
        (
            'Your Connect platform is attempting to share an unverified bank account '
            'with a connected account.'
        ),
    )
    BANK_ACCOUNT_VERIFICATION_FAILED = (
        'bank_account_verification_failed',
        (
            'The bank account cannot be verified, either because the microdeposit '
            'amounts provided do not match the actual amounts, or because verification has '
            'failed too many times.'
        ),
    )
    BILLING_INVALID_MANDATE = (
        'billing_invalid_mandate',
        (
            'The Subscription or Invoice attempted payment on a PaymentMethod '
            'without an active mandate. In order to create Subscription or Invoice payments '
            'with this PaymentMethod, it must be confirmed on-session with a '
            'PaymentIntent or SetupIntent first.'
        ),
    )
    BITCOIN_UPGRADE_REQUIRED = (
        'bitcoin_upgrade_required',
        (
            'This method for creating Bitcoin payments is not supported anymore. '
            'Please upgrade your integration to use Sources instead.'
        ),
    )
    CARD_DECLINE_RATE_LIMIT_EXCEEDED = (
        'card_decline_rate_limit_exceeded',
        (
            'This card has been declined too many times. You can try to charge this '
            'card again after 24 hours. We suggest reaching out to your customer to make sure '
            'they have entered all of their information correctly and that there are no issues '
            'with their card.'
        ),
    )
    CARD_DECLINED = (
        'card_declined',
        (
            'The card has been declined. When a card is declined, the error returned also '
            'includes the decline_code attribute with the reason why the card was declined. '
            'Refer to our decline codes documentation to learn more.'
        ),
    )
    CARDHOLDER_PHONE_NUMBER_REQUIRED = (
        'cardholder_phone_number_required',
        (
            'You must have a phone_number on file for Issuing Cardholders who will be '
            'creating EU cards. You cannot create EU cards without a phone_number on file '
            'for the cardholder. See the 3D Secure Documenation for more details.'
        ),
    )
    CHARGE_ALREADY_CAPTURED = (
        'charge_already_captured',
        (
            'The charge you\'re attempting to capture has already been captured. '
            'Update the request with an uncaptured charge ID.'
        ),
    )
    CHARGE_ALREADY_REFUNDED = (
        'charge_already_refunded',
        (
            'The charge you\'re attempting to refund has already been refunded. '
            'Update the request to use the ID of a charge that has not been refunded.'
        ),
    )
    CHARGE_DISPUTED = (
        'charge_disputed',
        (
            'The charge you\'re attempting to refund has been charged back. '
            'Check the disputes documentation to learn how to respond to the dispute.'
        ),
    )
    CHARGE_EXCEEDS_SOURCE_LIMIT = (
        'charge_exceeds_source_limit',
        (
            'This charge would cause you to exceed your rolling-window processing limit for '
            'this source type. Please retry the charge later, or contact us to '
            'request a higher processing limit.'
        ),
    )
    CHARGE_EXPIRED_FOR_CAPTURE = (
        'charge_expired_for_capture',
        (
            'The charge cannot be captured as the authorization has expired. '
            'Auth and capture charges must be captured within a set number of days (7 by default).'
        ),
    )
    CHARGE_INVALID_PARAMETER = (
        'charge_invalid_parameter',
        (
            'One or more provided parameters was not allowed for the given operation '
            'on the Charge. Check our API reference or the returned error message to see which '
            'values were not correct for that Charge.'
        ),
    )
    CLEARING_CODE_UNSUPPORTED = (
        'clearing_code_unsupported',
        'The clearing code provided is not supported.',
    )
    COUNTRY_CODE_INVALID = ('country_code_invalid', 'The country code provided was invalid.')
    COUNTRY_UNSUPPORTED = (
        'country_unsupported',
        (
            'Your platform attempted to create a custom account in a country that is '
            'not yet supported. Make sure that users can only sign up in countries '
            'supported by custom accounts.'
        ),
    )
    COUPON_EXPIRED = (
        'coupon_expired',
        (
            'The coupon provided for a subscription or order has expired. '
            'Either create a new coupon, or use an existing one that is valid.'
        ),
    )
    CUSTOMER_MAX_PAYMENT_METHODS = (
        'customer_max_payment_methods',
        (
            'The maximum number of PaymentMethods for this Customer has been reached. '
            'Either detach some PaymentMethods from this Customer or proceed with a different '
            'Customer.'
        ),
    )
    CUSTOMER_MAX_SUBSCRIPTIONS = (
        'customer_max_subscriptions',
        (
            'The maximum number of subscriptions for a customer has been reached. '
            'Contact us if you are receiving this error.'
        ),
    )
    DEBIT_NOT_AUTHORIZED = (
        'debit_not_authorized',
        'The customer has notified their bank that this payment was unauthorized.',
    )
    EMAIL_INVALID = (
        'email_invalid',
        (
            'The email address is invalid (e.g., not properly formatted). '
            'Check that the email address is properly formatted and only includes '
            'allowed characters.'
        ),
    )
    EXPIRED_CARD = (
        'expired_card',
        'The card has expired. Check the expiration date or use a different card.',
    )
    IDEMPOTENCY_KEY_IN_USE = (
        'idempotency_key_in_use',
        (
            'The idempotency key provided is currently being used in another request. '
            'This occurs if your integration is making duplicate requests simultaneously.'
        ),
    )
    INCORRECT_ADDRESS = (
        'incorrect_address',
        'The card\'s address is incorrect. Check the card\'s address or use a different card.',
    )
    INCORRECT_CVC = (
        'incorrect_cvc',
        (
            'The card\'s security code is incorrect. '
            'Check the card\'s security code or use a different card.'
        ),
    )
    INCORRECT_NUMBER = (
        'incorrect_number',
        'The card number is incorrect. Check the card\'s number or use a different card.',
    )
    INCORRECT_ZIP = (
        'incorrect_zip',
        (
            'The card\'s postal code is incorrect. '
            'Check the card\'s postal code or use a different card.'
        ),
    )
    INSTANT_PAYOUTS_UNSUPPORTED = (
        'instant_payouts_unsupported',
        (
            'This card is not eligible for Instant Payouts. '
            'Try a debit card from a supported bank.'
        ),
    )
    INSUFFICIENT_FUNDS = (
        'insufficient_funds',
        'The customer\'s account has insufficient funds to cover this payment.',
    )
    INTENT_INVALID_STATE = (
        'intent_invalid_state',
        'Intent is not in the state that is required to perform the operation.',
    )
    INTENT_VERIFICATION_METHOD_MISSING = (
        'intent_verification_method_missing',
        (
            'Intent does not have verification method specified in its '
            'PaymentMethodOptions object.'
        ),
    )
    INVALID_CARD_TYPE = (
        'invalid_card_type',
        (
            'The card provided as an external account is not supported for payouts. '
            'Provide a non-prepaid debit card instead.'
        ),
    )
    INVALID_CHARACTERS = (
        'invalid_characters',
        (
            'This value provided to the field contains characters that are '
            'unsupported by the field.'
        ),
    )
    INVALID_CHARGE_AMOUNT = (
        'invalid_charge_amount',
        (
            'The specified amount is invalid. The charge amount must be a positive '
            'integer in the smallest currency unit, and not exceed the minimum or maximum amount.'
        ),
    )
    INVALID_CVC = (
        'invalid_cvc',
        (
            'The card\'s security code is invalid. Check the card\'s security '
            'code or use a different card.'
        ),
    )
    INVALID_EXPIRY_MONTH = (
        'invalid_expiry_month',
        (
            'The card\'s expiration month is incorrect. Check the expiration '
            'date or use a different card.'
        ),
    )
    INVALID_EXPIRY_YEAR = (
        'invalid_expiry_year',
        (
            'The card\'s expiration year is incorrect. Check the expiration '
            'date or use a different card.'
        ),
    )
    INVALID_NUMBER = (
        'invalid_number',
        'The card number is invalid. Check the card details or use a different card.',
    )
    INVALID_SOURCE_USAGE = (
        'invalid_source_usage',
        (
            'The source cannot be used because it is not in the correct state '
            '(e.g., a charge request is trying to use a source with a pending, failed, '
            'or consumed source). Check the status of the source you are attempting to use.'
        ),
    )
    INVOICE_NO_CUSTOMER_LINE_ITEMS = (
        'invoice_no_customer_line_items',
        (
            'An invoice cannot be generated for the specified customer as there are no '
            'pending invoice items. Check that the correct customer is being specified or '
            'create any necessary invoice items first.'
        ),
    )
    INVOICE_NO_PAYMENT_METHOD_TYPES = (
        'invoice_no_payment_method_types',
        (
            'An invoice cannot be finalized because there are no payment method types '
            'available to process the payment. Your invoice template settings or the '
            'invoice\'s payment_settings might be restricting which payment methods are '
            'available, or you might need to activate more payment methods in the Dashboard.'
        ),
    )
    INVOICE_NO_SUBSCRIPTION_LINE_ITEMS = (
        'invoice_no_subscription_line_items',
        (
            'An invoice cannot be generated for the specified subscription as there are no '
            'pending invoice items. Check that the correct subscription is being specified '
            'or create any necessary invoice items first.'
        ),
    )
    INVOICE_NOT_EDITABLE = (
        'invoice_not_editable',
        (
            'The specified invoice can no longer be edited. Instead, consider creating '
            'additional invoice items that will be applied to the next invoice. You can '
            'either manually generate the next invoice or wait for it to be automatically '
            'generated at the end of the billing cycle.'
        ),
    )
    INVOICE_ON_BEHALF_OF_NOT_EDITABLE = (
        'invoice_on_behalf_of_not_editable',
        (
            'You cannot update the on_behalf_of property of an invoice after the '
            'invoice has been assigned a number.'
        ),
    )
    INVOICE_PAYMENT_INTENT_REQUIRES_ACTION = (
        'invoice_payment_intent_requires_action',
        (
            'This payment requires additional user action before it can be completed '
            'successfully. Payment can be completed using the PaymentIntent associated '
            'with the invoice. See this page for more details.'
        ),
    )
    INVOICE_UPCOMING_NONE = (
        'invoice_upcoming_none',
        (
            'There is no upcoming invoice on the specified customer to preview. '
            'Only customers with active subscriptions or pending invoice items have '
            'invoices that can be previewed.'
        ),
    )
    LIVEMODE_MISMATCH = (
        'livemode_mismatch',
        (
            'Test and live mode API keys, requests, and objects are only available '
            'within the mode they are in.'
        ),
    )
    LOCK_TIMEOUT = (
        'lock_timeout',
        (
            'This object cannot be accessed right now because another API request or '
            'Stripe process is currently accessing it. If you see this error intermittently, '
            'retry the request. If you see this error frequently and are making multiple '
            'concurrent requests to a single object, make your requests serially or at a '
            'lower rate. See the rate limit documentation for more details.'
        ),
    )
    MISSING = (
        'missing',
        (
            'Both a customer and source ID have been provided, but the source has not been '
            'saved to the customer. To create a charge for a customer with a specified source, '
            'you must first save the card details.'
        ),
    )
    NO_ACCOUNT = ('no_account', 'The bank account could not be located.')
    NOT_ALLOWED_ON_STANDARD_ACCOUNT = (
        'not_allowed_on_standard_account',
        'Transfers and payouts on behalf of a Standard connected account are not allowed.',
    )
    ORDER_CREATION_FAILED = (
        'order_creation_failed',
        'The order could not be created. Check the order details and then try again.',
    )
    ORDER_REQUIRED_SETTINGS = (
        'order_required_settings',
        (
            'The order could not be processed as it is missing required information. '
            'Check the information provided and try again.'
        ),
    )
    ORDER_STATUS_INVALID = (
        'order_status_invalid',
        (
            'The order cannot be updated because the status provided is either '
            'invalid or does not follow the order lifecycle (e.g., an order cannot '
            'transition from created to fulfilled without first transitioning to paid).'
        ),
    )
    ORDER_UPSTREAM_TIMEOUT = (
        'order_upstream_timeout',
        ('The request timed out. Try again later.'),
    )
    OUT_OF_INVENTORY = (
        'out_of_inventory',
        (
            'One or more line item(s) are out of stock. If more stock is available, '
            'update the inventory\'s orderable quantity and try again.'
        ),
    )
    PARAMETER_INVALID_EMPTY = (
        'parameter_invalid_empty',
        (
            'One or more required values were not provided. Make sure requests include '
            'all required parameters.'
        ),
    )
    PARAMETER_INVALID_INTEGER = (
        'parameter_invalid_integer',
        (
            'One or more of the parameters requires an integer, but the values provided were a '
            'different type. Make sure that only supported values are provided '
            'for each attribute. Refer to our API documentation to look up the type of '
            'data each attribute supports.'
        ),
    )
    PARAMETER_INVALID_STRING_BLANK = (
        'parameter_invalid_string_blank',
        (
            'One or more values provided only included whitespace. '
            'Check the values in your request and update any that contain only whitespace.'
        ),
    )
    PARAMETER_INVALID_STRING_EMPTY = (
        'parameter_invalid_string_empty',
        (
            'One or more required string values is empty. '
            'Make sure that string values contain at least one character.'
        ),
    )
    PARAMETER_MISSING = (
        'parameter_missing',
        (
            'One or more required values are missing. '
            'Check our API documentation to see which values are required to create or '
            'modify the specified resource.'
        ),
    )
    PARAMETER_UNKNOWN = (
        'parameter_unknown',
        'The request contains one or more unexpected parameters. Remove these and try again.',
    )
    PARAMETERS_EXCLUSIVE = (
        'parameters_exclusive',
        (
            'Two or more mutually exclusive parameters were provided. '
            'Check our API documentation or the returned error message to see which '
            'values are permitted when creating or modifying the specified resource.'
        ),
    )
    PAYMENT_INTENT_ACTION_REQUIRED = (
        'payment_intent_action_required',
        (
            'The provided payment method requires customer actions to complete, '
            'but error_on_requires_action was set. If you\'d like to add this payment method to '
            'your integration, we recommend that you first upgrade your integration to '
            'handle actions.'
        ),
    )
    PAYMENT_INTENT_AUTHENTICATION_FAILURE = (
        'payment_intent_authentication_failure',
        (
            'The provided payment method has failed authentication. '
            'Provide a new payment method to attempt to fulfill this PaymentIntent again.'
        ),
    )
    PAYMENT_INTENT_INCOMPATIBLE_PAYMENT_METHOD = (
        'payment_intent_incompatible_payment_method',
        (
            'The PaymentIntent expected a payment method with different properties '
            'than what was provided.'
        ),
    )
    PAYMENT_INTENT_INVALID_PARAMETER = (
        'payment_intent_invalid_parameter',
        (
            'One or more provided parameters was not allowed for the given operation on the '
            'PaymentIntent. Check our API reference or the returned error message to see '
            'which values were not correct for that PaymentIntent.'
        ),
    )
    PAYMENT_INTENT_MANDATE_INVALID = (
        'payment_intent_mandate_invalid',
        'The provided mandate is invalid and can not be used for the payment intent.',
    )
    PAYMENT_INTENT_PAYMENT_ATTEMPT_EXPIRED = (
        'payment_intent_payment_attempt_expired',
        (
            'The latest payment attempt for the PaymentIntent has expired. '
            'Check the last_payment_error property on the PaymentIntent for more details, '
            'and provide a new payment method to attempt to fulfill this PaymentIntent again.'
        ),
    )
    PAYMENT_INTENT_PAYMENT_ATTEMPT_FAILED = (
        'payment_intent_payment_attempt_failed',
        (
            'The latest payment attempt for the PaymentIntent has failed. '
            'Check the last_payment_error property on the PaymentIntent for more details, '
            'and provide a new payment method to attempt to fulfill this PaymentIntent again.'
        ),
    )
    PAYMENT_INTENT_UNEXPECTED_STATE = (
        'payment_intent_unexpected_state',
        (
            'The PaymentIntent\'s state was incompatible with the operation you were trying '
            'to perform.'
        ),
    )
    PAYMENT_METHOD_BANK_ACCOUNT_ALREADY_VERIFIED = (
        'payment_method_bank_account_already_verified',
        'This bank account has already been verified.',
    )
    PAYMENT_METHOD_BANK_ACCOUNT_BLOCKED = (
        'payment_method_bank_account_blocked',
        (
            'This bank account has failed verification in the past and can not be used. '
            'Contact us if you wish to attempt to use these bank account credentials.'
        ),
    )
    PAYMENT_METHOD_BILLING_DETAILS_ADDRESS_MISSING = (
        'payment_method_billing_details_address_missing',
        (
            'The PaymentMethod\'s billing details is missing address details. '
            'Please update the missing fields and try again.'
        ),
    )
    PAYMENT_METHOD_CURRENCY_MISMATCH = (
        'payment_method_currency_mismatch',
        (
            'The currency specified does not match the currency for the attached payment method. '
            'A payment can only be created for the same currency as the corresponding payment '
            'method.'
        ),
    )
    PAYMENT_METHOD_INVALID_PARAMETER = (
        'payment_method_invalid_parameter',
        (
            'Invalid parameter was provided in the payment method object. '
            'Check our API documentation or the returned error message for more context.'
        ),
    )
    PAYMENT_METHOD_MICRODEPOSIT_FAILED = (
        'payment_method_microdeposit_failed',
        (
            'Microdeposits were failed to be deposited into the customer\'s bank account. '
            'Please check the account, institution and transit numbers as well as the '
            'currency type.'
        ),
    )
    PAYMENT_METHOD_MICRODEPOSIT_VERIFICATION_AMOUNTS_INVALID = (
        'payment_method_microdeposit_verification_amounts_invalid',
        'You must provide exactly two microdeposit amounts.',
    )
    PAYMENT_METHOD_MICRODEPOSIT_VERIFICATION_AMOUNTS_MISMATCH = (
        'payment_method_microdeposit_verification_amounts_mismatch',
        'The amounts provided do not match the amounts that were sent to the bank account.',
    )
    PAYMENT_METHOD_MICRODEPOSIT_VERIFICATION_ATTEMPTS_EXCEEDED = (
        'payment_method_microdeposit_verification_attempts_exceeded',
        'You have exceeded the number of allowed verification attempts.',
    )
    PAYMENT_METHOD_MICRODEPOSIT_VERIFICATION_DESCRIPTOR_CODE_MISMATCH = (
        'payment_method_microdeposit_verification_descriptor_code_mismatch',
        'The verification code provided does not match the one sent to the bank account.',
    )
    PAYMENT_METHOD_MICRODEPOSIT_VERIFICATION_TIMEOUT = (
        'payment_method_microdeposit_verification_timeout',
        'Payment method should be verified with microdeposits within the required period.',
    )
    PAYMENT_METHOD_PROVIDER_DECLINE = (
        'payment_method_provider_decline',
        (
            'The payment was declined by the issuer or customer. '
            'Check the last_payment_error property on the PaymentIntent for more details, '
            'and provide a new payment method to attempt to fulfill this PaymentIntent again.'
        ),
    )
    PAYMENT_METHOD_PROVIDER_TIMEOUT = (
        'payment_method_provider_timeout',
        (
            'The payment method failed due to a timeout. '
            'Check the last_payment_error property on the PaymentIntent for more details, '
            'and provide a new payment method to attempt to fulfill this PaymentIntent again.'
        ),
    )
    PAYMENT_METHOD_UNACTIVATED = (
        'payment_method_unactivated',
        (
            'The operation cannot be performed as the payment method used has not been activated. '
            'Activate the payment method in the Dashboard, then try again.'
        ),
    )
    PAYMENT_METHOD_UNEXPECTED_STATE = (
        'payment_method_unexpected_state',
        (
            'The provided payment method\'s state was incompatible with the operation '
            'you were trying to perform. Confirm that the payment method is in an allowed '
            'state for the given operation before attempting to perform it.'
        ),
    )
    PAYMENT_METHOD_UNSUPPORTED_TYPE = (
        'payment_method_unsupported_type',
        'The API only supports payment methods of certain types.',
    )
    PAYOUTS_NOT_ALLOWED = (
        'payouts_not_allowed',
        (
            'Payouts have been disabled on the connected account. '
            'Check the connected account\'s status to see if any additional information '
            'needs to be provided, or if payouts have been disabled for another reason.'
        ),
    )
    PLATFORM_ACCOUNT_REQUIRED = (
        'platform_account_required',
        (
            'Only Stripe Connect platforms can work with other accounts. '
            'If you need to setup a Stripe Connect platform, you can do so in the dashboard.'
        ),
    )
    PLATFORM_API_KEY_EXPIRED = (
        'platform_api_key_expired',
        (
            'The API key provided by your Connect platform has expired. '
            'This occurs if your platform has either generated a new key or the connected '
            'account has been disconnected from the platform. Obtain your current API keys '
            'from the Dashboard and update your integration, or reach out to the user and '
            'reconnect the account.'
        ),
    )
    POSTAL_CODE_INVALID = ('postal_code_invalid', 'The postal code provided was incorrect.')
    PROCESSING_ERROR = (
        'processing_error',
        (
            'An error occurred while processing the card. Try again later or with a '
            'different payment method.'
        ),
    )
    PRODUCT_INACTIVE = (
        'product_inactive',
        'The product this SKU belongs to is no longer available for purchase.',
    )
    RATE_LIMIT = (
        'rate_limit',
        (
            'Too many requests hit the API too quickly. We recommend an exponential '
            'backoff of your requests.'
        ),
    )
    REFER_TO_CUSTOMER = (
        'refer_to_customer',
        (
            'The customer has stopped the payment with their bank. Contact them for '
            'details and to arrange payment.'
        ),
    )
    RESOURCE_ALREADY_EXISTS = (
        'resource_already_exists',
        (
            'A resource with a user-specified ID (e.g., plan or coupon) already exists. '
            'Use a different, unique value for id and try again.'
        ),
    )
    RESOURCE_MISSING = (
        'resource_missing',
        (
            'The ID provided is not valid. Either the resource does not exist, '
            'or an ID for a different resource has been provided.'
        ),
    )
    ROUTING_NUMBER_INVALID = (
        'routing_number_invalid',
        'The bank routing number provided is invalid.',
    )
    SECRET_KEY_REQUIRED = (
        'secret_key_required',
        (
            'The API key provided is a publishable key, but a secret key is required. '
            'Obtain your current API keys from the Dashboard and update your integration '
            'to use them.'
        ),
    )
    SENSITIVE_DATA_ACCESS_EXPIRED = (
        'sensitive_data_access_expired',
        (
            'This information is available for a limited amount of time. If you are receiving '
            'this error, that window has passed.'
        ),
    )
    SEPA_UNSUPPORTED_ACCOUNT = (
        'sepa_unsupported_account',
        ('Your account does not support SEPA payments.'),
    )
    SETUP_ATTEMPT_FAILED = (
        'setup_attempt_failed',
        (
            'The latest setup attempt for the SetupIntent has failed. Check the '
            'last_setup_error property on the SetupIntent for more details, and provide '
            'a new payment method to attempt to set it up again.'
        ),
    )
    SETUP_INTENT_AUTHENTICATION_FAILURE = (
        'setup_intent_authentication_failure',
        (
            'The provided payment method has failed authentication. Provide a new payment '
            'method to attempt to fulfill this SetupIntent again.'
        ),
    )
    SETUP_INTENT_INVALID_PARAMETER = (
        'setup_intent_invalid_parameter',
        (
            'One or more provided parameters was not allowed for the given operation on the '
            'SetupIntent. Check our API reference or the returned error message to see which '
            'values were not correct for that SetupIntent.'
        ),
    )
    SETUP_INTENT_UNEXPECTED_STATE = (
        'setup_intent_unexpected_state',
        (
            'The SetupIntent\'s state was incompatible with the operation you were trying '
            'to perform.'
        ),
    )
    SHIPPING_CALCULATION_FAILED = (
        'shipping_calculation_failed',
        (
            'Shipping calculation failed as the information provided was either incorrect '
            'or could not be verified.'
        ),
    )
    SKU_INACTIVE = (
        'sku_inactive',
        (
            'The SKU is inactive and no longer available for purchase. Use a different SKU, '
            'or make the current SKU active again.'
        ),
    )
    STATE_UNSUPPORTED = (
        'state_unsupported',
        (
            'Occurs when providing the legal_entity information for a U.S. custom account, '
            'if the provided state is not supported. (This is mostly associated states and '
            'territories.)'
        ),
    )
    TAX_ID_INVALID = (
        'tax_id_invalid',
        (
            'The tax ID number provided is invalid (e.g., missing digits). '
            'Tax ID information varies from country to country, but must be at least nine digits.'
        ),
    )
    TAXES_CALCULATION_FAILED = (
        'taxes_calculation_failed',
        'Tax calculation for the order failed.',
    )
    TERMINAL_LOCATION_COUNTRY_UNSUPPORTED = (
        'terminal_location_country_unsupported',
        (
            'Terminal is currently only available in some countries. '
            'Locations in your country cannot be created in livemode.'
        ),
    )
    TESTMODE_CHARGES_ONLY = (
        'testmode_charges_only',
        (
            'Your account has not been activated and can only make test charges. '
            'Activate your account in the Dashboard to begin processing live charges.'
        ),
    )
    TLS_VERSION_UNSUPPORTED = (
        'tls_version_unsupported',
        (
            'Your integration is using an older version of TLS that is unsupported. '
            'You must be using TLS 1.2 or above.'
        ),
    )
    TOKEN_ALREADY_USED = (
        'token_already_used',
        (
            'The token provided has already been used. You must create a new token '
            'before you can retry this request.'
        ),
    )
    TOKEN_IN_USE = (
        'token_in_use',
        (
            'The token provided is currently being used in another request. '
            'This occurs if your integration is making duplicate requests simultaneously.'
        ),
    )
    TRANSFERS_NOT_ALLOWED = (
        'transfers_not_allowed',
        'The requested transfer cannot be created. Contact us if you are receiving this error.',
    )
    UPSTREAM_ORDER_CREATION_FAILED = (
        'upstream_order_creation_failed',
        'The order could not be created. Check the order details and then try again.',
    )
    URL_INVALID = ('url_invalid', 'The URL provided is invalid.')
